import { service } from '@/config/axios/service'

export interface PublicDictDataItem {
  label: string
  value: string
  colorType?: string
  cssClass?: string
}

// Type for the raw response item from the backend
interface RawPublicDictDataItem extends PublicDictDataItem {
  dictType: string
}

// The response will be a map where the key is the dict type
export type PublicDictsResponse = Record<string, PublicDictDataItem[]>

/**
 * Get public dictionary data.
 * This function calls the backend endpoint which returns a flat array of dict items,
 * and then transforms it into a map grouped by dictType.
 *
 * @param dictTypes Array of dictionary types
 */
export const getPublicDictsApi = async (dictTypes: string[]): Promise<PublicDictsResponse> => {
  // The service interceptor returns the `data` property from the axios response,
  // which in our case is an object like { code, data: [], msg }.
  const response = await service.get('/hr/person/dict-data/list', {
    params: {
      types: dictTypes.join(',')
    },
    headers: {
      isToken: false // Explicitly tell the interceptor not to add the token
    }
  })

  // The actual dictionary array is in `response.data`
  const rawDicts: RawPublicDictDataItem[] = response.data || []

  // Transform the flat array into a map
  const dictMap: PublicDictsResponse = {}
  if (Array.isArray(rawDicts)) {
    rawDicts.forEach((item) => {
      const { dictType, ...rest } = item
      if (!dictMap[dictType]) {
        dictMap[dictType] = []
      }
      dictMap[dictType].push(rest)
    })
  }
  return dictMap
}
