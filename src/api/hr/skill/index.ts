import request from '@/config/axios'

/** 技能标签信息 */
export interface Skill {
          id: number; // ID
          code: string; // 技能编码
          name?: string; // 技能名称
          category: string; // 技能分类
  }

// 技能标签 API
export const SkillApi = {
  // 查询技能标签分页
  getSkillPage: async (params: any) => {
    return await request.get({ url: `/hr/skill-catalog/page`, params })
  },

  // 查询技能标签详情
  getSkill: async (id: number) => {
    return await request.get({ url: `/hr/skill-catalog/get?id=` + id })
  },

  // 新增技能标签
  createSkill: async (data: Skill) => {
    return await request.post({ url: `/hr/skill-catalog/create`, data })
  },

  // 修改技能标签
  updateSkill: async (data: Skill) => {
    return await request.put({ url: `/hr/skill-catalog/update`, data })
  },

  // 删除技能标签
  deleteSkill: async (id: number) => {
    return await request.delete({ url: `/hr/skill-catalog/delete?id=` + id })
  },

  /** 批量删除技能标签 */
  deleteSkillList: async (ids: number[]) => {
    return await request.delete({ url: `/hr/skill-catalog/delete-list?ids=${ids.join(',')}` })
  },

  // 导出技能标签 Excel
  exportSkill: async (params) => {
    return await request.download({ url: `/hr/skill-catalog/export-excel`, params })
  }
}
