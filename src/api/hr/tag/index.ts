import request from '@/config/axios'

/** 标签规则信息 */
export interface TagRule {
  id: number; // ID
  tagId: number; // 标签ID
  name: string; // 规则名称
  type: string; // 规则类型
  content: string; // 规则内容
  params: string; // 规则参数
  enabled: boolean; // 启用状态
  displayOrder: number; // 显示顺序
}

/** 标签目录信息 */
export interface TagCatalog {
  id: number; // ID
  code?: string; // 标签编码
  name: string; // 标签名称
  category: string; // 标签分类
  type: string; // 标签类型
  description: string; // 标签描述
  color: string; // 标签颜色
  icon: string; // 标签图标
  displayOrder: number; // 显示顺序
}

// 标签目录 API
export const TagCatalogApi = {
  // 查询标签目录分页
  getTagCatalogPage: async (params: any) => {
    return await request.get({ url: `/hr/tag-catalog/page`, params })
  },

  // 查询标签目录详情
  getTagCatalog: async (id: number) => {
    return await request.get({ url: `/hr/tag-catalog/get?id=` + id })
  },

  // 新增标签目录
  createTagCatalog: async (data: TagCatalog) => {
    return await request.post({ url: `/hr/tag-catalog/create`, data })
  },

  // 修改标签目录
  updateTagCatalog: async (data: TagCatalog) => {
    return await request.put({ url: `/hr/tag-catalog/update`, data })
  },

  // 删除标签目录
  deleteTagCatalog: async (id: number) => {
    return await request.delete({ url: `/hr/tag-catalog/delete?id=` + id })
  },

  /** 批量删除标签目录 */
  deleteTagCatalogList: async (ids: number[]) => {
    return await request.delete({ url: `/hr/tag-catalog/delete-list?ids=${ids.join(',')}` })
  },

  // 导出标签目录 Excel
  exportTagCatalog: async (params) => {
    return await request.download({ url: `/hr/tag-catalog/export-excel`, params })
  },

// ==================== 子表（标签规则） ====================

  // 获得标签规则分页
  getTagRulePage: async (params) => {
    return await request.get({ url: `/hr/tag-rule/page`, params })
  },
  // 新增标签规则
  createTagRule: async (data: TagRule) => {
    return await request.post({ url: `/hr/tag-rule/create`, data })
  },

  // 修改标签规则
  updateTagRule: async (data: TagRule) => {
    return await request.put({ url: `/hr/tag-rule/update`, data })
  },

  // 删除标签规则
  deleteTagRule: async (id: number) => {
    return await request.delete({ url: `/hr/tag-rule/delete?id=` + id })
  },

  /** 批量删除标签规则 */
  deleteTagRuleList: async (ids: number[]) => {
    return await request.delete({ url: `/hr/tag-rule/delete-list?ids=${ids.join(',')}` })
  },

  // 获得标签规则
  getTagRule: async (id: number) => {
    return await request.get({ url: `/hr/tag-rule/get?id=` + id })
  }
}
