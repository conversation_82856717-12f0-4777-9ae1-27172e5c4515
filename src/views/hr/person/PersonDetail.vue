<template>
  <el-drawer
    v-model="drawerVisible"
    :title="drawerTitle"
    direction="rtl"
    size="60%"
    :destroy-on-close="true"
  >
    <ContentWrap>
      <div v-if="formData" class="resume-container" v-loading="loading">
        <!-- Header -->
        <div class="resume-header">
          <div class="header-left">
            <h1 class="name">{{ formData.name }}</h1>
            <p class="position">{{ formData.position }}</p>
          </div>
          <div class="header-right">
            <el-avatar :size="100" :src="formData.avatarUrl"/>
          </div>
        </div>

        <!-- Main Content -->
        <div class="resume-content">
          <!-- 基本信息 -->
          <el-card class="box-card" shadow="never">
            <template #header>
              <div class="card-header">
                <span><Icon icon="ep:user" class="mr-5px"/>基本信息</span>
              </div>
            </template>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="性别">
                <dict-tag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="formData.gender"/>
              </el-descriptions-item>
              <el-descriptions-item label="年龄">{{ formData.age }}</el-descriptions-item>
              <el-descriptions-item label="出生日期">{{
                  formatDate(formData.birthDate)
                }}
              </el-descriptions-item>
              <el-descriptions-item label="民族">
                <dict-tag :type="DICT_TYPE.ETHNICITY" :value="formData.ethnicity"/>
              </el-descriptions-item>
              <el-descriptions-item label="政治面貌">
                <dict-tag :type="DICT_TYPE.POLITICAL_STATUS" :value="formData.politicalStatus"/>
              </el-descriptions-item>
              <el-descriptions-item label="婚姻状况">
                <dict-tag :type="DICT_TYPE.MARITAL_STATUS" :value="formData.maritalStatus"/>
              </el-descriptions-item>
              <el-descriptions-item label="国籍">
                <dict-tag :type="DICT_TYPE.NATIONALITY" :value="formData.nationality"/>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>

          <!-- 联系方式 -->
          <el-card class="box-card" shadow="never">
            <template #header>
              <div class="card-header">
                <span><Icon icon="ep:phone" class="mr-5px"/>联系方式</span>
              </div>
            </template>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="手机号码">{{ formData.phone }}</el-descriptions-item>
              <el-descriptions-item label="邮箱地址">{{ formData.email }}</el-descriptions-item>
              <el-descriptions-item label="身份证号">{{ formData.idCard }}</el-descriptions-item>
              <el-descriptions-item label="城市">{{ formData.city }}</el-descriptions-item>
              <el-descriptions-item label="现居住地址" :span="2">{{
                  formData.address
                }}
              </el-descriptions-item>
              <el-descriptions-item label="户籍地址" :span="2">{{
                  formData.registeredAddress
                }}
              </el-descriptions-item>
            </el-descriptions>
          </el-card>

          <!-- 求职意向 -->
          <el-card class="box-card" shadow="never" v-if="jobPrefs.length > 0">
            <template #header>
              <div class="card-header">
                <span><Icon icon="ep:aim" class="mr-5px"/>求职意向</span>
              </div>
            </template>
            <el-table :data="jobPrefs" border stripe>
              <el-table-column prop="expectedPosition" label="期望职位"/>
              <el-table-column prop="expectedIndustry" label="期望行业"/>
              <el-table-column prop="expectedCity" label="期望工作城市"/>
              <el-table-column prop="expectedSalary" label="期望薪资"/>
            </el-table>
          </el-card>

          <!-- 教育背景 -->
          <el-card class="box-card" shadow="never" v-if="educationHistory.length > 0">
            <template #header>
              <div class="card-header">
                <span><Icon icon="ep:school" class="mr-5px"/>教育背景</span>
              </div>
            </template>
            <el-table :data="educationHistory" border stripe>
              <el-table-column prop="schoolName" align="center" label="学校名称"/>
              <el-table-column prop="startDate" align="center" label="开始时间" :formatter="row => formatDate(row.startDate,'YYYY-MM-DD')"/>
              <el-table-column prop="endDate" align="center" label="结束时间" :formatter="row => formatDate(row.endDate,'YYYY-MM-DD')"/>
              <el-table-column prop="educationLevel" align="center" label="学历">
                <template #default="{ row }">
                  <dict-tag :type="DICT_TYPE.EDUCATION_LEVEL" :value="row.educationLevel"/>
                </template>
              </el-table-column>
              <el-table-column prop="degree" align="center" label="学位">
                <template #default="{ row }">
                  <dict-tag :type="DICT_TYPE.DEGREE" :value="row.degree"/>
                </template>
              </el-table-column>
              <el-table-column prop="major" align="center" label="专业"/>
            </el-table>
          </el-card>

          <!-- 工作经历 -->
          <el-card class="box-card" shadow="never" v-if="workHistory.length > 0">
            <template #header>
              <div class="card-header">
                <span><Icon icon="ep:briefcase" class="mr-5px"/>工作经历</span>
              </div>
            </template>
            <el-table :data="workHistory" border stripe>
              <el-table-column prop="companyName" align="center" label="公司名称"/>
              <!--              <el-table-column prop="industry" align="center" label="行业"/>-->
              <el-table-column prop="position" align="center" label="职位" width="140"/>
              <el-table-column prop="startDate" align="center" label="开始时间" width="120" :formatter="row => formatDate(row.startDate,'YYYY-MM-DD')"/>
              <el-table-column prop="endDate" align="center" label="结束时间" width="120" :formatter="row => formatDate(row.endDate,'YYYY-MM-DD')"/>
            </el-table>
          </el-card>


          <!-- 项目经历 -->
          <el-card class="box-card" shadow="never" v-if="projectHistory.length > 0">
            <template #header>
              <div class="card-header">
                <span><Icon icon="ep:finished" class="mr-5px"/>项目经历</span>
              </div>
            </template>
            <div class="project-list">
              <el-card
                v-for="(project, index) in projectHistory"
                :key="index"
                class="project-card"
                shadow="never"
              >
                <div class="project-card-header">
                  <span class="project-name">{{ project.projectName }}</span>
                  <span class="project-duration">
                    {{ formatDate(project.startDate, 'YYYY/MM') }} -
                    {{ formatDate(project.endDate, 'YYYY/MM') }}
                  </span>
                </div>
                <div class="project-role-company">
                  <span>{{ project.companyName }}</span>
                  <el-divider v-if="project.projectRole" direction="vertical" />
                  <span v-if="project.projectRole">{{ project.projectRole }}</span>
                </div>
                <el-descriptions :column="1" border class="project-details-desc" style="margin-top: 10px">
                  <el-descriptions-item label="项目描述">
                    {{ project.projectDesc }}
                  </el-descriptions-item>
                  <el-descriptions-item label="项目职责">
                    {{ project.projectResponsibility }}
                  </el-descriptions-item>
                  <el-descriptions-item label="项目业绩">
                    {{ project.projectAchievement }}
                  </el-descriptions-item>
                </el-descriptions>
              </el-card>
            </div>
          </el-card>

          <!-- 技能特长 -->
          <el-card class="box-card" shadow="never" v-if="skills.length > 0">
            <template #header>
              <div class="card-header">
                <span><Icon icon="ep:cpu" class="mr-5px"/>技能特长</span>
              </div>
            </template>
            <el-table :data="skills" border stripe>
              <el-table-column prop="skillName" label="技能名称"/>
              <el-table-column prop="proficiencyLevel" label="熟练程度">
                <template #default="{ row }">
                  <dict-tag :type="DICT_TYPE.PROFICIENCY_LEVEL" :value="row.proficiencyLevel"/>
                </template>
              </el-table-column>
              <el-table-column prop="yearsOfExperience" label="使用年限"/>
            </el-table>
          </el-card>

          <!-- 资格证书 -->
          <el-card class="box-card" shadow="never" v-if="certificates.length > 0">
            <template #header>
              <div class="card-header">
                <span><Icon icon="ep:document" class="mr-5px"/>资格证书</span>
              </div>
            </template>
            <el-table :data="certificates" border stripe>
              <el-table-column prop="certificateName" label="证书名称"/>
              <el-table-column prop="certificateCode" label="证书编号"/>
              <el-table-column prop="issuer" label="发证机构"/>
              <el-table-column prop="issueDate" label="颁发日期" :formatter="row => formatDate(row.issueDate)"/>
              <el-table-column prop="expiryDate" label="有效期至" :formatter="row => formatDate(row.expiryDate)"/>
            </el-table>
          </el-card>

          <!-- 语言能力 -->
          <el-card class="box-card" shadow="never" v-if="languages.length > 0">
            <template #header>
              <div class="card-header">
                <span><Icon icon="ep:translate" class="mr-5px"/>语言能力</span>
              </div>
            </template>
            <el-table :data="languages" border stripe>
              <el-table-column prop="languageName" label="语言">
                <template #default="{ row }">
                  <dict-tag :type="DICT_TYPE.LANGUAGE_NAME" :value="row.languageName"/>
                </template>
              </el-table-column>
              <el-table-column prop="proficiencyLevel" label="熟练程度">
                <template #default="{ row }">
                  <dict-tag :type="DICT_TYPE.PROFICIENCY_LEVEL" :value="row.proficiencyLevel"/>
                </template>
              </el-table-column>
            </el-table>
          </el-card>

        </div>
      </div>
      <el-empty v-else description="暂无数据"/>
    </ContentWrap>
  </el-drawer>
</template>

<script setup lang="ts">
import {ref} from 'vue'
import {PersonApi, Person} from '@/api/hr/person'
import {DICT_TYPE} from '@/utils/dict'
import {formatDate} from '@/utils/formatTime'
import {Icon} from '@/components/Icon'

defineOptions({name: 'PersonDetail'})

const drawerVisible = ref(false)
const drawerTitle = ref('人员详情')
const formData = ref<Person | null>(null)
const loading = ref(false)

// Sub-table data
const jobPrefs = ref<any[]>([])
const workHistory = ref<any[]>([])
const educationHistory = ref<any[]>([])
const projectHistory = ref<any[]>([])
const skills = ref<any[]>([])
const certificates = ref<any[]>([])
const languages = ref<any[]>([])

/**
 * Opens the drawer with person details.
 * @param id The ID of the person.
 */
const open = async (id: number) => {
  drawerVisible.value = true
  loading.value = true
  // Reset states
  formData.value = null
  jobPrefs.value = []
  workHistory.value = []
  educationHistory.value = []
  projectHistory.value = []
  skills.value = []
  certificates.value = []
  languages.value = []

  try {
    // Fetch all data in parallel
    const [
      personData,
      jobPrefData,
      workHistoryData,
      educationHistoryData,
      projectHistoryData,
      skillData,
      certificateData,
      languageData
    ] = await Promise.all([
      PersonApi.getPerson(id),
      PersonApi.getPersonJobPrefListByPersonId(id),
      PersonApi.getPersonWorkListByPersonId(id),
      PersonApi.getPersonEducationListByPersonId(id),
      PersonApi.getPersonProjectListByPersonId(id),
      PersonApi.getPersonSkillListByPersonId(id),
      PersonApi.getPersonCertificateListByPersonId(id),
      PersonApi.getPersonLanguageListByPersonId(id)
    ])

    formData.value = personData
    drawerTitle.value = `${personData.name}的简历`
    jobPrefs.value = jobPrefData
    workHistory.value = workHistoryData
    educationHistory.value = educationHistoryData
    projectHistory.value = projectHistoryData
    skills.value = skillData
    certificates.value = certificateData
    languages.value = languageData

  } catch (error) {
    console.error("Failed to load person details:", error)
  } finally {
    loading.value = false
  }
}
defineExpose({open})

</script>

<style lang="scss" scoped>
.resume-container {
  padding: 20px;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
  '\\5FAE软雅黑', Arial, sans-serif;
}

.resume-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f0f2f5;

  .header-left {
    .name {
      font-size: 28px;
      font-weight: 600;
      margin-bottom: 10px;
    }

    .position {
      font-size: 18px;
      color: #606266;
    }
  }
}

.resume-content {
  .box-card {
    margin-bottom: 20px;
    border: none;
    border-radius: 4px;
    background-color: #ffffff;

    .card-header {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }
}

:deep(.el-descriptions__label) {
  font-weight: bold;
  width: 120px;
}

.project-list {
  .project-card {
    border: 1px solid #e0e0e0;
    margin-bottom: 15px;
    &:last-child {
      margin-bottom: 0;
    }
  }

  .project-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .project-name {
      font-size: 16px;
      font-weight: bold;
    }

    .project-duration {
      color: #909399;
      font-size: 14px;
    }
  }

  .project-role-company {
    font-size: 14px;
    color: #606266;
    margin-bottom: 10px;
  }

  .project-details-desc {
    :deep(.el-descriptions__label) {
      width: 100px;
      font-weight: normal;
      color: #909399;
    }
    :deep(.el-descriptions__content) {
      color: #303133;
    }
  }
}
</style>
