<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px" border stripe>
      <el-table-column label="序号" type="index" align="center" width="60" />
      <el-table-column label="学校名称" min-width="150" align="center">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="editingIndex === $index"
            :prop="`${$index}.schoolName`"
            :rules="formRules.schoolName"
            class="mb-0px!"
          >
            <el-input v-model="row.schoolName" placeholder="请输入学校名称" />
          </el-form-item>
          <span v-else>{{ row.schoolName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" width="120" align="center">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="editingIndex === $index"
            :prop="`${$index}.startDate`"
            :rules="formRules.startDate"
            class="mb-0px!"
          >
            <el-date-picker
              v-model="row.startDate"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="选择开始时间"
            />
          </el-form-item>
          <span v-else>{{ formatDate(row.startDate, 'YYYY-MM-DD') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" width="120" align="center">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="editingIndex === $index"
            :prop="`${$index}.endDate`"
            :rules="formRules.endDate"
            class="mb-0px!"
          >
            <el-date-picker
              v-model="row.endDate"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="选择结束时间"
            />
          </el-form-item>
          <span v-else>{{ formatDate(row.endDate, 'YYYY-MM-DD') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="专业" min-width="150" align="center">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="editingIndex === $index"
            :prop="`${$index}.major`"
            :rules="formRules.major"
            class="mb-0px!"
          >
            <el-input v-model="row.major" placeholder="请输入专业" />
          </el-form-item>
          <span v-else>{{ row.major }}</span>
        </template>
      </el-table-column>
      <el-table-column label="学历" width="130" align="center">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="editingIndex === $index"
            :prop="`${$index}.educationLevel`"
            :rules="formRules.educationLevel"
            class="mb-0px!"
          >
            <el-select v-model="row.educationLevel" placeholder="请选择">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.EDUCATION_LEVEL)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <dict-tag v-else :type="DICT_TYPE.EDUCATION_LEVEL" :value="row.educationLevel" />
        </template>
      </el-table-column>
      <el-table-column label="学位" width="130" align="center">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="editingIndex === $index"
            :prop="`${$index}.degree`"
            :rules="formRules.degree"
            class="mb-0px!"
          >
            <el-select v-model="row.degree" placeholder="请选择">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.DEGREE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <dict-tag v-else :type="DICT_TYPE.DEGREE" :value="row.degree" />
        </template>
      </el-table-column>
      <el-table-column label="学历证编号" width="180" align="center">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="editingIndex === $index"
            :prop="`${$index}.educationCertNo`"
            :rules="formRules.educationCertNo"
            class="mb-0px!"
          >
            <el-input v-model="row.educationCertNo" placeholder="请输入学历证编号" />
          </el-form-item>
          <span v-else>{{ row.educationCertNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="学位证编号" width="180" align="center">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="editingIndex === $index"
            :prop="`${$index}.degreeCertNo`"
            :rules="formRules.degreeCertNo"
            class="mb-0px!"
          >
            <el-input v-model="row.degreeCertNo" placeholder="请输入学位证编号" />
          </el-form-item>
          <span v-else>{{ row.degreeCertNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="学历证文件" width="200" align="center">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="editingIndex === $index"
            :prop="`${$index}.educationCertFile`"
            :rules="formRules.educationCertFile"
            class="mb-0px!"
          >
            <div class="flex items-center justify-center gap-4">
              <el-upload
                :action="uploadUrl"
                :http-request="uploadHttpRequest"
                :show-file-list="false"
                :on-success="(res) => handleEducationCertFileUpload(res, row)"
              >
                <el-button size="small" type="primary">点击上传</el-button>
              </el-upload>
              <el-link
                v-if="row.educationCertFile"
                type="primary"
                :underline="false"
                @click="previewFile(row.educationCertFile, `${row.schoolName}-学历证文件`)"
                >预览</el-link
              >
            </div>
          </el-form-item>
          <div v-else-if="row.educationCertFile" class="flex items-center justify-center gap-4">
            <el-link
              type="primary"
              :underline="false"
              @click="previewFile(row.educationCertFile, `${row.schoolName}-学历证文件`)"
              >预览</el-link
            >
            <el-link
              type="primary"
              :underline="false"
              @click="downloadFile(row.educationCertFile, `${row.schoolName}-学历证文件`)"
              >下载</el-link
            >
          </div>
        </template>
      </el-table-column>
      <el-table-column label="学位证文件" width="200" align="center">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="editingIndex === $index"
            :prop="`${$index}.degreeCertFile`"
            :rules="formRules.degreeCertFile"
            class="mb-0px!"
          >
            <div class="flex items-center justify-center gap-4">
              <el-upload
                :action="uploadUrl"
                :http-request="uploadHttpRequest"
                :show-file-list="false"
                :on-success="(res) => handleDegreeCertFileUpload(res, row)"
              >
                <el-button size="small" type="primary">点击上传</el-button>
              </el-upload>
              <el-link
                v-if="row.degreeCertFile"
                type="primary"
                :underline="false"
                @click="previewFile(row.degreeCertFile, `${row.schoolName}-学位证文件`)"
                >预览</el-link
              >
            </div>
          </el-form-item>
          <div v-else-if="row.degreeCertFile" class="flex items-center justify-center gap-4">
            <el-link
              type="primary"
              :underline="false"
              @click="previewFile(row.degreeCertFile, `${row.schoolName}-学位证文件`)"
              >预览</el-link
            >
            <el-link
              type="primary"
              :underline="false"
              @click="downloadFile(row.degreeCertFile, `${row.schoolName}-学位证文件`)"
              >下载</el-link
            >
          </div>
        </template>
      </el-table-column>
      <el-table-column label="在校情况" min-width="150" align="center">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="editingIndex === $index"
            :prop="`${$index}.schoolExperience`"
            :rules="formRules.schoolExperience"
            class="mb-0px!"
          >
            <el-input v-model="row.schoolExperience" placeholder="请输入在校情况" />
          </el-form-item>
          <span v-else>{{ row.schoolExperience }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否统招" width="100" align="center">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="editingIndex === $index"
            :prop="`${$index}.isFullTime`"
            :rules="formRules.isFullTime"
            class="mb-0px!"
          >
            <el-switch v-model="row.isFullTime" />
          </el-form-item>
          <dict-tag v-else :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="row.isFullTime" />
        </template>
      </el-table-column>
      <el-table-column label="学历证书电子注册备案表" width="200" align="center">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="editingIndex === $index"
            :prop="`${$index}.educationVerifyFile`"
            :rules="formRules.educationVerifyFile"
            class="mb-0px!"
          >
            <div class="flex items-center justify-center gap-4">
              <el-upload
                :action="uploadUrl"
                :http-request="uploadHttpRequest"
                :show-file-list="false"
                :on-success="(res) => handleEducationVerifyFileUpload(res, row)"
              >
                <el-button size="small" type="primary">点击上传</el-button>
              </el-upload>
              <el-link
                v-if="row.educationVerifyFile"
                type="primary"
                :underline="false"
                @click="
                  previewFile(row.educationVerifyFile, `${row.schoolName}-学历证书电子注册备案表`)
                "
                >预览</el-link
              >
            </div>
          </el-form-item>
          <div v-else-if="row.educationVerifyFile" class="flex items-center justify-center gap-4">
            <el-link
              type="primary"
              :underline="false"
              @click="
                previewFile(row.educationVerifyFile, `${row.schoolName}-学历证书电子注册备案表`)
              "
              >预览</el-link
            >
            <el-link
              type="primary"
              :underline="false"
              @click="
                downloadFile(row.educationVerifyFile, `${row.schoolName}-学历证书电子注册备案表`)
              "
              >下载</el-link
            >
          </div>
        </template>
      </el-table-column>
      <el-table-column label="学位在线验证报告" width="200" align="center">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="editingIndex === $index"
            :prop="`${$index}.degreeVerifyFile`"
            :rules="formRules.degreeVerifyFile"
            class="mb-0px!"
          >
            <div class="flex items-center justify-center gap-4">
              <el-upload
                :action="uploadUrl"
                :http-request="uploadHttpRequest"
                :show-file-list="false"
                :on-success="(res) => handleDegreeVerifyFileUpload(res, row)"
              >
                <el-button size="small" type="primary">点击上传</el-button>
              </el-upload>
              <el-link
                v-if="row.degreeVerifyFile"
                type="primary"
                :underline="false"
                @click="previewFile(row.degreeVerifyFile, `${row.schoolName}-学位在线验证报告`)"
                >预览</el-link
              >
            </div>
          </el-form-item>
          <div v-else-if="row.degreeVerifyFile" class="flex items-center justify-center gap-4">
            <el-link
              type="primary"
              :underline="false"
              @click="previewFile(row.degreeVerifyFile, `${row.schoolName}-学位在线验证报告`)"
              >预览</el-link
            >
            <el-link
              type="primary"
              :underline="false"
              @click="downloadFile(row.degreeVerifyFile, `${row.schoolName}-学位在线验证报告`)"
              >下载</el-link
            >
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="120">
        <template #default="{ row, $index }">
          <div v-if="editingIndex === $index">
            <el-button @click="handleSave($index)" type="primary" link>保存</el-button>
            <el-button @click="handleCancel($index)" type="danger" link>取消</el-button>
          </div>
          <div v-else>
            <el-button @click="handleEdit(row, $index)" type="primary" link>编辑</el-button>
            <el-button @click="handleDelete($index)" type="danger" link>删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加教育经历</el-button>
  </el-row>
</template>
<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { PersonApi } from '@/api/hr/person'
import { formatDate } from '@/utils/formatTime'
import { useUpload } from '@/components/UploadFile/src/useUpload'
import { openFilePreview } from '@/utils/filePreview'

const props = defineProps<{
  personId: number // 人员ID（主表的关联字段）
}>()
const message = useMessage() // 消息弹窗
const formLoading = ref(false) // 表单的加载中
const formData = ref<any[]>([])
const formRules = reactive({
  personId: [{ required: true, message: '人员ID不能为空', trigger: 'blur' }],
  schoolName: [{ required: true, message: '学校名称不能为空', trigger: 'blur' }],
  startDate: [{ required: true, message: '开始时间不能为空', trigger: 'blur' }],
  endDate: [{ required: true, message: '结束时间不能为空', trigger: 'blur' }],
  major: [{ required: true, message: '专业不能为空', trigger: 'blur' }],
  educationLevel: [{ required: true, message: '学历不能为空', trigger: 'change' }],
  degree: [{ required: true, message: '学位不能为空', trigger: 'change' }],
  educationCertNo: [{ required: false, message: '学历证编号不能为空', trigger: 'blur' }],
  educationCertFile: [{ required: false, message: '学历证文件不能为空', trigger: 'blur' }],
  degreeCertNo: [{ required: false, message: '学位证编号不能为空', trigger: 'blur' }],
  degreeCertFile: [{ required: false, message: '学位证文件不能为空', trigger: 'blur' }],
  schoolExperience: [{ required: false, message: '在校情况不能为空', trigger: 'blur' }],
  isFullTime: [{ required: false, message: '是否统招不能为空', trigger: 'blur' }],
  educationVerifyFile: [
    { required: false, message: '学历证书电子注册备案表不能为空', trigger: 'blur' }
  ],
  degreeVerifyFile: [{ required: false, message: '学位在线验证报告不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

const editingIndex = ref<number | null>(null)
const originalData = ref<any | null>(null)

const { uploadUrl, httpRequest: uploadHttpRequest } = useUpload('hr-education')

const handleEducationCertFileUpload = (response: any, row: any) => {
  row.educationCertFile = response.data
  message.success('上传成功')
}

const handleDegreeCertFileUpload = (response: any, row: any) => {
  row.degreeCertFile = response.data
  message.success('上传成功')
}

const handleEducationVerifyFileUpload = (response: any, row: any) => {
  row.educationVerifyFile = response.data
  message.success('上传成功')
}

const handleDegreeVerifyFileUpload = (response: any, row: any) => {
  row.degreeVerifyFile = response.data
  message.success('上传成功')
}

const previewFile = (fileUrl: string, title: string) => {
  if (!fileUrl) {
    message.warning('文件不存在')
    return
  }
  openFilePreview(fileUrl, title)
}

const downloadFile = (fileUrl: string, title: string) => {
  if (!fileUrl) {
    message.warning('文件不存在')
    return
  }
  const fileExtension = fileUrl.split('.').pop() || 'pdf'
  const fileName = `${title}.${fileExtension}`
  const link = document.createElement('a')
  link.href = fileUrl
  link.download = fileName
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.personId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    editingIndex.value = null
    // 2. val 非空，则加载数据
    if (!val) {
      return
    }
    try {
      formLoading.value = true
      formData.value = await PersonApi.getPersonEducationListByPersonId(val)
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)

/** 编辑按钮操作 */
const handleEdit = (row: any, index: number) => {
  if (editingIndex.value !== null) {
    message.warning('请先保存或取消当前编辑的行')
    return
  }
  originalData.value = JSON.parse(JSON.stringify(row))
  editingIndex.value = index
}

/** 保存按钮操作 */
const handleSave = async (index: number) => {
  try {
    const fieldsToValidate = Object.keys(formRules).map((key) => `${index}.${key}`)
    await formRef.value.validateField(fieldsToValidate)
    editingIndex.value = null
    originalData.value = null
    message.success('保存成功')
  } catch (error) {
    message.error('表单校验不通过')
  }
}

/** 取消按钮操作 */
const handleCancel = (index: number) => {
  if (originalData.value && originalData.value.isNew) {
    formData.value.splice(index, 1)
  } else {
    formData.value[index] = originalData.value
  }
  editingIndex.value = null
  originalData.value = null
  // 清除校验结果
  formRef.value.clearValidate()
}

/** 新增按钮操作 */
const handleAdd = () => {
  if (editingIndex.value !== null) {
    message.warning('请先保存或取消当前编辑的行')
    return
  }
  const newRow = {
    id: undefined,
    personId: props.personId,
    schoolName: undefined,
    startDate: undefined,
    endDate: undefined,
    major: undefined,
    educationLevel: undefined,
    degree: undefined,
    educationCertNo: undefined,
    educationCertFile: undefined,
    educationVerifyCode: undefined,
    degreeCertNo: undefined,
    degreeCertFile: undefined,
    degreeVerifyCode: undefined,
    schoolExperience: undefined,
    isFullTime: false,
    educationVerifyFile: undefined,
    degreeVerifyFile: undefined,
    isNew: true // 标记为新行
  }
  formData.value.push(newRow)
  const newIndex = formData.value.length - 1
  handleEdit(newRow, newIndex)
}

/** 删除按钮操作 */
const handleDelete = (index: number) => {
  if (editingIndex.value === index) {
    editingIndex.value = null
    originalData.value = null
  }
  formData.value.splice(index, 1)
}

/** 表单校验 */
const validate = async () => {
  if (editingIndex.value !== null) {
    message.warning('请先保存或取消正在编辑的教育经历')
    return Promise.reject('请先保存或取消正在编辑的教育经历')
  }
  return formRef.value.validate()
}

/** 表单值 */
const getData = () => {
  return formData.value.map((item) => {
    const newItem = { ...item }
    delete newItem.isNew // 移除isNew标记
    return newItem
  })
}

defineExpose({ validate, getData })
</script>

<style scoped>
/* 减少textarea的padding，增加可输入字符空间 */
:deep(.el-textarea__inner) {
  padding: 3px 6px !important;
}

/* 减少表格单元格的左右padding */
:deep(.el-table--default .cell) {
  padding-left: 6px !important;
  padding-right: 6px !important;
}
</style>
