<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px" border stripe>
      <el-table-column label="序号" type="index" width="60" align="center"/>
      <el-table-column label="公司名称" min-width="150" align="center">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="editingIndex === $index"
            :prop="`${$index}.companyName`"
            :rules="formRules.companyName"
            class="mb-0px!"
          >
            <el-input v-model="row.companyName" placeholder="请输入公司名称"/>
          </el-form-item>
          <span v-else>{{ row.companyName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" width="140" align="center">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="editingIndex === $index"
            :prop="`${$index}.startDate`"
            :rules="formRules.startDate"
            class="mb-0px!"
          >
            <el-date-picker
              v-model="row.startDate"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="选择开始时间"
            />
          </el-form-item>
          <span v-else>{{ formatDate(row.startDate, 'YYYY-MM-DD') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" width="140" align="center">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="editingIndex === $index"
            :prop="`${$index}.endDate`"
            :rules="formRules.endDate"
            class="mb-0px!"
          >
            <el-date-picker
              v-model="row.endDate"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="选择结束时间"
            />
          </el-form-item>
          <span v-else>{{ formatDate(row.endDate, 'YYYY-MM-DD') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="行业" min-width="150" align="center">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="editingIndex === $index"
            :prop="`${$index}.industry`"
            :rules="formRules.industry"
            class="mb-0px!"
          >
            <el-input v-model="row.industry" placeholder="请输入行业"/>
          </el-form-item>
          <span v-else>{{ row.industry }}</span>
        </template>
      </el-table-column>
      <el-table-column label="职位" width="150" align="center">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="editingIndex === $index"
            :prop="`${$index}.position`"
            :rules="formRules.position"
            class="mb-0px!"
          >
            <el-input v-model="row.position" placeholder="请输入职位"/>
          </el-form-item>
          <span v-else>{{ row.position }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="120">
        <template #default="{ row, $index }">
          <div v-if="editingIndex === $index">
            <el-button @click="handleSave($index)" type="primary" link>保存</el-button>
            <el-button @click="handleCancel($index)" type="danger" link>取消</el-button>
          </div>
          <div v-else>
            <el-button @click="handleEdit(row, $index)" type="primary" link>编辑</el-button>
            <el-button @click="handleDelete($index)" type="danger" link>删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加工作经历</el-button>
  </el-row>
</template>
<script setup lang="ts">
import {PersonApi} from '@/api/hr/person'
import {formatDate} from '@/utils/formatTime'

const props = defineProps<{
  personId: number // 人员ID（主表的关联字段）
}>()
const message = useMessage() // 消息弹窗
const formLoading = ref(false) // 表单的加载中
const formData = ref<any[]>([])
const formRules = reactive({
  personId: [{required: true, message: '人员ID不能为空', trigger: 'blur'}],
  startDate: [{required: true, message: '开始时间不能为空', trigger: 'blur'}],
  endDate: [{required: true, message: '结束时间不能为空', trigger: 'blur'}],
  companyName: [{required: false, message: '公司名称不能为空', trigger: 'blur'}],
  industry: [{required: false, message: '行业不能为空', trigger: 'blur'}],
  position: [{required: false, message: '职位不能为空', trigger: 'blur'}]
})
const formRef = ref() // 表单 Ref

const editingIndex = ref<number | null>(null)
const originalData = ref<any | null>(null)

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.personId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    editingIndex.value = null
    // 2. val 非空，则加载数据
    if (!val) {
      return
    }
    try {
      formLoading.value = true
      formData.value = await PersonApi.getPersonWorkListByPersonId(val)
    } finally {
      formLoading.value = false
    }
  },
  {immediate: true}
)

/** 编辑按钮操作 */
const handleEdit = (row: any, index: number) => {
  if (editingIndex.value !== null) {
    message.warning('请先保存或取消当前编辑的行')
    return
  }
  originalData.value = JSON.parse(JSON.stringify(row))
  editingIndex.value = index
}

/** 保存按钮操作 */
const handleSave = async (index: number) => {
  try {
    const fieldsToValidate = Object.keys(formRules).map((key) => `${index}.${key}`)
    await formRef.value.validateField(fieldsToValidate)
    editingIndex.value = null
    originalData.value = null
    message.success('保存成功')
  } catch (error) {
    message.error('表单校验不通过')
  }
}

/** 取消按钮操作 */
const handleCancel = (index: number) => {
  if (originalData.value && originalData.value.isNew) {
    formData.value.splice(index, 1)
  } else {
    formData.value[index] = originalData.value
  }
  editingIndex.value = null
  originalData.value = null
  // 清除校验结果
  formRef.value.clearValidate()
}

/** 新增按钮操作 */
const handleAdd = () => {
  if (editingIndex.value !== null) {
    message.warning('请先保存或取消当前编辑的行')
    return
  }
  const newRow = {
    id: undefined,
    personId: props.personId,
    companyName: undefined,
    startDate: undefined,
    endDate: undefined,
    industry: undefined,
    position: undefined,
    isNew: true // 标记为新行
  }
  formData.value.push(newRow)
  const newIndex = formData.value.length - 1
  handleEdit(newRow, newIndex)
}

/** 删除按钮操作 */
const handleDelete = (index: number) => {
  if (editingIndex.value === index) {
    editingIndex.value = null
    originalData.value = null
  }
  formData.value.splice(index, 1)
}

/** 表单校验 */
const validate = async () => {
  if (editingIndex.value !== null) {
    message.warning('请先保存或取消正在编辑的工作经历')
    return Promise.reject('请先保存或取消正在编辑的工作经历')
  }
  return formRef.value.validate()
}

/** 表单值 */
const getData = () => {
  return formData.value.map((item) => {
    const newItem = {...item}
    delete newItem.isNew // 移除isNew标记
    return newItem
  })
}

defineExpose({validate, getData})
</script>

<style scoped>
/* 减少textarea的padding，增加可输入字符空间 */
:deep(.el-textarea__inner) {
  padding: 3px 6px !important;
}

/* 减少表格单元格的左右padding */
:deep(.el-table--default .cell) {
  padding-left: 6px !important;
  padding-right: 6px !important;
}

:deep(.el-input__wrapper) {
  padding: 1px 6px;
}
</style>
