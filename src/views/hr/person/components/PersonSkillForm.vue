'''<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px" border stripe>
      <el-table-column label="序号" type="index" width="60" align="center" />
      <el-table-column label="技能名称" min-width="300" align="center">
        <template #default="{ row, $index }">
          <div v-if="editingIndex === $index" class="w-full flex items-start gap-2">
            <!-- Skill Select -->
            <el-form-item :prop="`${$index}.skillId`" :rules="formRules.skillId" class="flex-1 mb-0px!">
              <el-select
                v-model="row.skillId"
                placeholder="请选择或搜索技能"
                filterable
                remote
                :remote-method="remoteSearchSkills"
                :loading="skillLoading"
                class="w-full"
                @change="handleSkillChange(row, $index)"
                @visible-change="handleSkillSelectVisibleChange"
              >
                <el-option
                  v-for="item in skillList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
                <template #footer>
                  <div v-if="skillTotal > skillList.length - 1" class="px-2 py-1 text-center">
                    <el-button
                      v-if="!skillLoading && skillQuery.pageNo * skillQuery.pageSize < skillTotal"
                      @click="loadMoreSkills"
                      text
                      size="small"
                    >
                      加载更多 ({{ skillList.length - 1 }}/{{ skillTotal }})
                    </el-button>
                    <span v-else-if="skillLoading" class="text-gray-500">加载中...</span>
                    <span v-else class="text-gray-500">已加载全部</span>
                  </div>
                </template>
              </el-select>
            </el-form-item>
            <!-- Custom Skill Input -->
            <el-form-item
              v-if="row.skillId === CUSTOM_SKILL_ID"
              :prop="`${$index}.skillName`"
              :rules="formRules.skillName"
              class="flex-1 mb-0px!"
            >
              <el-input v-model="row.skillName" placeholder="请输入自定义技能名称" />
            </el-form-item>
          </div>
          <span v-else>{{ row.skillName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="熟练程度" width="200" align="center">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="editingIndex === $index"
            :prop="`${$index}.proficiencyLevel`"
            :rules="formRules.proficiencyLevel"
            class="mb-0px!"
          >
            <el-select v-model="row.proficiencyLevel" placeholder="请选择熟练程度">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.PROFICIENCY_LEVEL)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <dict-tag v-else :type="DICT_TYPE.PROFICIENCY_LEVEL" :value="row.proficiencyLevel" />
        </template>
      </el-table-column>
      <el-table-column label="使用年限" width="150" align="center">
        <template #default="{ row, $index }">
          <el-form-item
            v-if="editingIndex === $index"
            :prop="`${$index}.yearsOfExperience`"
            :rules="formRules.yearsOfExperience"
            class="mb-0px!"
          >
            <el-input-number
              v-model="row.yearsOfExperience"
              placeholder="请输入年限"
              controls-position="right"
              style="width: 120px"
            />
          </el-form-item>
          <span v-else>{{ row.yearsOfExperience }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="120">
        <template #default="{ row, $index }">
          <div v-if="editingIndex === $index">
            <el-button @click="handleSave($index)" type="primary" link>保存</el-button>
            <el-button @click="handleCancel($index)" type="danger" link>取消</el-button>
          </div>
          <div v-else>
            <el-button @click="handleEdit(row, $index)" type="primary" link>编辑</el-button>
            <el-button @click="handleDelete($index)" type="danger" link>删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加技能特长</el-button>
  </el-row>
</template>
<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { PersonApi } from '@/api/hr/person'
import { SkillApi, type Skill } from '@/api/hr/skill'
import { ref, reactive, watch } from 'vue'

// 使用 0 作为“自定义技能”在前端的唯一标识符，提交时会转换回 null
const CUSTOM_SKILL_ID = 0
const CUSTOM_SKILL_OBJECT = { id: CUSTOM_SKILL_ID, name: '自定义技能' } as Skill

const props = defineProps<{
  personId: number // 人员ID（主表的关联字段）
}>()
const message = useMessage() // 消息弹窗
const formLoading = ref(false) // 表单的加载中
const formData = ref<any[]>([])

const editingIndex = ref<number | null>(null)
const originalData = ref<any | null>(null)

/** 自定义去重校验器 */
const duplicateValidator = (rule, value, callback) => {
  const index = parseInt(rule.field.split('.')[0])
  const currentRow = formData.value[index]

  const duplicate = formData.value.find((item, idx) => {
    if (index === idx) return false // 跳过自身

    // 检查预定义技能ID是否重复
    if (currentRow.skillId !== CUSTOM_SKILL_ID && item.skillId === currentRow.skillId) {
      return true
    }
    // 检查自定义技能名称是否重复（不区分大小写）
    if (
      currentRow.skillId === CUSTOM_SKILL_ID &&
      item.skillId === CUSTOM_SKILL_ID &&
      item.skillName?.toLowerCase() === currentRow.skillName?.toLowerCase()
    ) {
      return true
    }
    return false
  })

  if (duplicate) {
    callback(new Error('该技能已存在，请勿重复添加'))
  } else {
    callback()
  }
}

const formRules = reactive({
  skillId: [
    { required: true, message: '请选择一个技能', trigger: 'change' },
    { validator: duplicateValidator, trigger: 'change' }
  ],
  skillName: [
    { required: true, message: '自定义技能名称不能为空', trigger: 'blur' },
    { validator: duplicateValidator, trigger: 'blur' }
  ]
})
const formRef = ref() // 表单 Ref

const skillList = ref<Skill[]>([])
const skillLoading = ref(false)
const skillQuery = reactive({
  pageNo: 1,
  pageSize: 10,
  name: ''
})
const skillTotal = ref(0)
const allSkillPages = ref<Skill[]>([]) // 存储所有已加载的技能页面数据
const selectedSkillsCache = ref<Skill[]>([]) // 缓存已选中的技能数据

/**
 * [核心] 合并和去重技能列表
 * @param lists 要合并的技能数组列表
 */
const combineSkillLists = (...lists: Skill[][]): Skill[] => {
  const map = new Map<number, Skill>()
  lists.flat().forEach((skill) => {
    map.set(skill.id, skill)
  })
  return Array.from(map.values())
}

/**
 * 缓存已选中的技能数据
 */
const cacheSelectedSkills = async () => {
  const selectedSkillIds = formData.value
    .map((f) => f.skillId)
    .filter((id) => id !== CUSTOM_SKILL_ID && id !== undefined)

  if (selectedSkillIds.length === 0) {
    selectedSkillsCache.value = []
    return
  }

  // 只获取缓存中不存在的技能
  const cachedIds = selectedSkillsCache.value.map((s) => s.id)
  const newIds = selectedSkillIds.filter((id) => !cachedIds.includes(id))

  if (newIds.length > 0) {
    const newSkills = await Promise.all(newIds.map((id) => SkillApi.getSkill(id)))
    selectedSkillsCache.value = combineSkillLists(selectedSkillsCache.value, newSkills)
  }
}

/**
 * [核心] 获取并更新技能列表
 */
const fetchAndSetSkillList = async (resetPages = false) => {
  skillLoading.value = true
  try {
    const pageResult = await SkillApi.getSkillPage(skillQuery)
    skillTotal.value = pageResult.total

    // 如果是搜索或重置，清空之前的页面数据
    if (resetPages || skillQuery.name) {
      allSkillPages.value = [...pageResult.list]
    } else {
      // 合并新页面数据到已有数据中
      allSkillPages.value = combineSkillLists(allSkillPages.value, pageResult.list)
    }

    // 搜索时只显示搜索结果和自定义技能选项
    if (skillQuery.name) {
      skillList.value = combineSkillLists([CUSTOM_SKILL_OBJECT], allSkillPages.value)
    } else {
      // 非搜索时显示所有数据（包括已选中的技能）
      skillList.value = combineSkillLists(
        [CUSTOM_SKILL_OBJECT],
        selectedSkillsCache.value,
        allSkillPages.value
      )
    }
  } finally {
    skillLoading.value = false
  }
}

/**
 * 远程搜索技能
 */
const remoteSearchSkills = (name: string) => {
  skillQuery.name = name
  skillQuery.pageNo = 1
  fetchAndSetSkillList(true) // 搜索时重置页面数据
}

/**
 * 加载更多技能
 */
const loadMoreSkills = () => {
  skillQuery.pageNo++
  fetchAndSetSkillList(false)
}

/**
 * 下拉框可见性变化
 */
const handleSkillSelectVisibleChange = (visible: boolean) => {
  if (visible && !skillQuery.name) {
    // 下拉框打开且没有搜索条件时，如果还有更多数据，则加载
    if (skillList.value.length <= 1 && skillTotal.value > 0) {
      fetchAndSetSkillList(true)
    }
  }
}

/** 技能选择变更 */
const handleSkillChange = async (row, index) => {
  if (row.skillId === CUSTOM_SKILL_ID) {
    row.skillName = ''
  } else {
    const selectedSkill = skillList.value.find((s) => s.id === row.skillId)
    if (selectedSkill) {
      row.skillName = selectedSkill.name
      // 将新选中的技能添加到缓存中
      if (!selectedSkillsCache.value.find((s) => s.id === selectedSkill.id)) {
        selectedSkillsCache.value.push(selectedSkill)
      }
    }
  }
  // 清除或触发校验
  formRef.value.clearValidate([`${index}.skillName`])
  formRef.value.validateField([`${index}.skillId`])
}

/** [核心] 初始化 */
watch(
  () => props.personId,
  async (val) => {
    // 1. 重置状态
    formData.value = []
    editingIndex.value = null
    skillList.value = []
    allSkillPages.value = []
    selectedSkillsCache.value = []
    if (!val) return

    formLoading.value = true
    try {
      // 2. 加载并转换表单数据
      const rawData = await PersonApi.getPersonSkillListByPersonId(val)
      formData.value = rawData.map((item) => ({
        ...item,
        skillId: item.skillId ?? CUSTOM_SKILL_ID
      }))

      // 3. 缓存已选中的技能数据
      await cacheSelectedSkills()

      // 4. 重置查询条件并加载初始列表
      skillQuery.name = ''
      skillQuery.pageNo = 1
      await fetchAndSetSkillList(true) // 初始化时重置页面数据
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)

/** 编辑按钮操作 */
const handleEdit = (row: any, index: number) => {
  if (editingIndex.value !== null) {
    message.warning('请先保存或取消当前编辑的行')
    return
  }
  originalData.value = JSON.parse(JSON.stringify(row))
  editingIndex.value = index
}

/** 保存按钮操作 */
const handleSave = async (index: number) => {
  try {
    const row = formData.value[index]
    const fieldsToValidate = [`${index}.skillId`]
    if (row.skillId === CUSTOM_SKILL_ID) {
      fieldsToValidate.push(`${index}.skillName`)
    }
    await formRef.value.validateField(fieldsToValidate)
    editingIndex.value = null
    originalData.value = null
    message.success('保存成功')
  } catch (error) {
    message.error('表单校验不通过')
  }
}

/** 取消按钮操作 */
const handleCancel = (index: number) => {
  if (originalData.value && originalData.value.isNew) {
    formData.value.splice(index, 1)
  } else {
    formData.value[index] = originalData.value
  }
  editingIndex.value = null
  originalData.value = null
  // 清除校验结果
  formRef.value.clearValidate()
}

/** 新增按钮操作 */
const handleAdd = () => {
  if (editingIndex.value !== null) {
    message.warning('请先保存或取消当前编辑的行')
    return
  }
  const newRow = {
    id: undefined,
    personId: props.personId,
    skillId: undefined, // 默认为空，让用户选择
    skillName: '',
    proficiencyLevel: undefined,
    yearsOfExperience: undefined,
    isNew: true // 标记为新行
  }
  formData.value.push(newRow)
  const newIndex = formData.value.length - 1
  handleEdit(newRow, newIndex)
}

/** 删除按钮操作 */
const handleDelete = (index: number) => {
  if (editingIndex.value === index) {
    editingIndex.value = null
    originalData.value = null
  }
  formData.value.splice(index, 1)
}

/** 表单校验 */
const validate = async () => {
  if (editingIndex.value !== null) {
    message.warning('请先保存或取消正在编辑的技能特长')
    return Promise.reject('请先保存或取消正在编辑的技能特长')
  }
  return formRef.value.validate()
}

/** 表单值 */
const getData = () => {
  // 提交前，将前端的自定义标识符转换回后端需要的 null
  return formData.value.map((item) => ({
    ...item,
    skillId: item.skillId === CUSTOM_SKILL_ID ? null : item.skillId,
    isNew: undefined
  }))
}

defineExpose({ validate, getData })
</script>
''
