<template>
  <Dialog v-model="dialogVisible" title="填写邀请" width="500px">
    <div v-if="!generatedToken">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="80px"
        v-loading="formLoading"
      >
        <el-form-item label="姓名" prop="name">
          <el-input v-model="formData.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="formData.phone" placeholder="请输入手机号" />
        </el-form-item>
      </el-form>
    </div>
    <div v-else class="text-center">
      <p class="mb-4">请通过以下链接分享给候选人填写：</p>
      <el-input v-model="shareLink" readonly>
        <template #append>
          <el-button @click="copyLink" :icon="CopyDocument" />
        </template>
      </el-input>
      <div class="mt-4">
        <el-button @click="shareViaWeChat" circle>
          <Icon icon="fa-brands:weixin" />
        </el-button>
        <el-button @click="shareViaEmail" circle>
          <Icon icon="fa-solid:envelope" />
        </el-button>
      </div>
    </div>
    <template #footer>
      <el-button @click="dialogVisible = false">
        {{ generatedToken ? '关闭' : '取消' }}
      </el-button>
      <el-button v-if="!generatedToken" type="primary" @click="submitForm" :loading="formLoading">
        确定
      </el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElForm, ElMessage } from 'element-plus'
import { PersonApi, Person } from '@/api/hr/person'
import { useValidator } from '@/hooks/web/useValidator'
import { Icon } from '@/components/Icon'
import { CopyDocument } from '@element-plus/icons-vue'
import { useClipboard } from '@vueuse/core'

const { required } = useValidator()
const message = useMessage()
const emit = defineEmits(['success'])

const dialogVisible = ref(false)
const formLoading = ref(false)
const generatedToken = ref<string | null>(null)
const shareLink = ref('')

const formData = reactive({
  name: '',
  phone: ''
})

const formRules = reactive({
  name: [required()],
  phone: [required()]
})

const formRef = ref<InstanceType<typeof ElForm>>()

const open = async (person?: Person) => {
  resetForm()
  dialogVisible.value = true
  if (person && person.name && person.phone) {
    formLoading.value = true
    try {
      const token = await PersonApi.generatePersonAccessToken({
        id: person.id,
        name: person.name,
        phone: person.phone
      })
      generatedToken.value = token
      shareLink.value = `${window.location.origin}:3000/resume-wizard/${token}`
      emit('success')
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open })

const submitForm = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) return

  formLoading.value = true
  try {
    const token = await PersonApi.generatePersonAccessToken(formData)
    generatedToken.value = token
    shareLink.value = `${window.location.origin}:3000/resume-wizard/${token}`
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const resetForm = () => {
  formData.name = ''
  formData.phone = ''
  generatedToken.value = null
  shareLink.value = ''
  formRef.value?.resetFields()
}

const { copy } = useClipboard()
const copyLink = () => {
  copy(shareLink.value)
  ElMessage.success('链接已复制到剪贴板')
}

const shareViaWeChat = () => {
  //  微信分享逻辑
  ElMessage.info('请手动复制链接到微信中分享')
}

const shareViaEmail = () => {
  const subject = '邀请您填写信息'
  const body = `您好，请点击以下链接填写您的信息：${shareLink.value}`
  window.location.href = `mailto:?subject=${subject}&body=${encodeURIComponent(body)}`
}
</script>
