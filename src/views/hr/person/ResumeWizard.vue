<template>
  <div class="flex items-center justify-center min-h-screen bg-gray-100">
    <el-card class="w-full max-w-md" v-if="!isAuthenticated">
      <template #header>
        <div class="text-center">
          <h2 class="text-2xl font-bold">身份验证</h2>
        </div>
      </template>
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-position="top"
        @submit.prevent="validateAccess"
      >
        <el-form-item label="手机号码" prop="phone">
          <el-input
            v-model="formData.phone"
            placeholder="请输入您的手机号码"
            size="large"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            class="w-full"
            @click="validateAccess"
            :loading="loading"
            size="large"
          >
            验证
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <div v-else>
      <h1 class="text-3xl font-bold">欢迎，信息填写页面</h1>
      <!-- 后续的表单内容将在这里 -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElForm, ElMessage } from 'element-plus'
import { PersonApi } from '@/api/hr/person'
import { useValidator } from '@/hooks/web/useValidator'

const { required } = useValidator()
const route = useRoute()
const message = useMessage()

const isAuthenticated = ref(false)
const loading = ref(false)
const accessToken = ref<string | null>(null)

const formData = reactive({
  phone: ''
})

const formRules = reactive({
  phone: [required()]
})

const formRef = ref<InstanceType<typeof ElForm>>()

onMounted(() => {
  const token = route.params.token
  if (typeof token === 'string') {
    accessToken.value = token
  } else {
    message.error('无效的访问链接')
  }
})

const validateAccess = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) return

  if (!accessToken.value) {
    message.error('无效的访问令牌')
    return
  }

  loading.value = true
  try {
    const isValid = await PersonApi.validateAccessToken({
      phone: formData.phone,
      accessToken: accessToken.value
    })
    if (isValid) {
      isAuthenticated.value = true
      message.success('验证成功')
    } else {
      message.error('验证失败，请检查您的手机号码')
    }
  } catch (error) {
    console.error('验证访问令牌失败:', error)
    message.error('验证失败，请稍后重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* 可以根据需要添加一些样式 */
</style>
