<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="标签编码" prop="code">
        <el-input v-model="formData.code" placeholder="请输入标签编码" />
      </el-form-item>
      <el-form-item label="标签名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入标签名称" />
      </el-form-item>
      <el-form-item label="标签分类" prop="category">
        <el-select v-model="formData.category" placeholder="请选择标签分类">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.TAG_CATEGORY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="标签类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择标签类型">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.TAG_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="标签描述" prop="description">
        <Editor v-model="formData.description" height="150px" />
      </el-form-item>
      <el-form-item label="标签颜色" prop="color">
        <el-input v-model="formData.color" placeholder="请输入标签颜色" />
      </el-form-item>
      <el-form-item label="标签图标" prop="icon">
        <el-input v-model="formData.icon" placeholder="请输入标签图标" />
      </el-form-item>
      <el-form-item label="显示顺序" prop="displayOrder">
        <el-input v-model="formData.displayOrder" placeholder="请输入显示顺序" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { TagCatalogApi, TagCatalog } from '@/api/hr/tag'

/** 标签目录 表单 */
defineOptions({ name: 'TagCatalogForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  code: undefined,
  name: undefined,
  category: undefined,
  type: undefined,
  description: undefined,
  color: undefined,
  icon: undefined,
  displayOrder: undefined
})
const formRules = reactive({
  code: [{ required: true, message: '标签编码不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TagCatalogApi.getTagCatalog(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TagCatalog
    if (formType.value === 'create') {
      await TagCatalogApi.createTagCatalog(data)
      message.success(t('common.createSuccess'))
    } else {
      await TagCatalogApi.updateTagCatalog(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    name: undefined,
    category: undefined,
    type: undefined,
    description: undefined,
    color: undefined,
    icon: undefined,
    displayOrder: undefined
  }
  formRef.value?.resetFields()
}
</script>