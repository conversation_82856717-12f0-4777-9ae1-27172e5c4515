<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="标签编码" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入标签编码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="标签名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入标签名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="标签分类" prop="category">
        <el-select
          v-model="queryParams.category"
          placeholder="请选择"
          clearable
          class="!w-140px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.TAG_CATEGORY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="标签类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择"
          clearable
          class="!w-140px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.TAG_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px"/>
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px"/>
          重置
        </el-button>
      </el-form-item>
      <br/>
      <el-form-item>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['hr:tag-catalog:create']"
        >
          <Icon icon="ep:plus" class="mr-5px"/>
          新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['hr:tag-catalog:export']"
        >
          <Icon icon="ep:download" class="mr-5px"/>
          导出
        </el-button>
        <el-button
          type="danger"
          plain
          :disabled="isEmpty(checkedIds)"
          @click="handleDeleteBatch"
          v-hasPermi="['hr:tag-catalog:delete']"
        >
          <Icon icon="ep:delete" class="mr-5px"/>
          批量删除
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      row-key="id"
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      highlight-current-row
      @current-change="handleCurrentChange"
      @selection-change="handleRowCheckboxChange"
      border
    >
      <el-table-column type="selection" width="55"/>
      <el-table-column label="ID" align="center" prop="id" width="60"/>
<!--      <el-table-column label="标签编码" align="center" prop="code"/>-->
      <el-table-column label="标签名称" align="center" prop="name" width="200"/>
      <el-table-column label="标签分类" align="center" prop="category" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TAG_CATEGORY" :value="scope.row.category"/>
        </template>
      </el-table-column>
      <el-table-column label="标签类型" align="center" prop="type" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TAG_TYPE" :value="scope.row.type"/>
        </template>
      </el-table-column>
      <el-table-column label="标签描述" align="center" prop="description"/>
<!--      <el-table-column label="标签颜色" align="center" prop="color"/>-->
<!--      <el-table-column label="标签图标" align="center" prop="icon"/>-->
      <el-table-column label="顺序" align="center" prop="displayOrder" width="80"/>
<!--      <el-table-column-->
<!--        label="创建时间"-->
<!--        align="center"-->
<!--        prop="createTime"-->
<!--        :formatter="dateFormatter"-->
<!--        width="180px"-->
<!--      />-->
      <el-table-column label="操作" align="center" width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['hr:tag-catalog:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['hr:tag-catalog:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TagCatalogForm ref="formRef" @success="getList"/>
  <!-- 子表的列表 -->
  <ContentWrap>
    <el-tabs model-value="tagRule">
      <el-tab-pane label="标签规则" name="tagRule">
        <TagRuleList :tag-id="currentRow.id"/>
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>
</template>

<script setup lang="ts">
import {getStrDictOptions, DICT_TYPE} from '@/utils/dict'
import {isEmpty} from '@/utils/is'
import {dateFormatter} from '@/utils/formatTime'
import download from '@/utils/download'
import {TagCatalogApi, TagCatalog} from '@/api/hr/tag'
import TagCatalogForm from './TagCatalogForm.vue'
import TagRuleList from './components/TagRuleList.vue'

/** 标签目录 列表 */
defineOptions({name: 'TagCatalog'})

const message = useMessage() // 消息弹窗
const {t} = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<TagCatalog[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: undefined,
  name: undefined,
  category: undefined,
  type: undefined,
  description: undefined,
  color: undefined,
  icon: undefined,
  displayOrder: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TagCatalogApi.getTagCatalogPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TagCatalogApi.deleteTagCatalog(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {
  }
}

/** 批量删除标签目录 */
const handleDeleteBatch = async () => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    await TagCatalogApi.deleteTagCatalogList(checkedIds.value);
    message.success(t('common.delSuccess'))
    await getList();
  } catch {
  }
}

const checkedIds = ref<number[]>([])
const handleRowCheckboxChange = (records: TagCatalog[]) => {
  checkedIds.value = records.map((item) => item.id);
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TagCatalogApi.exportTagCatalog(queryParams)
    download.excel(data, '标签目录.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 选中行操作 */
const currentRow = ref({}) // 选中行
const handleCurrentChange = (row) => {
  currentRow.value = row
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
