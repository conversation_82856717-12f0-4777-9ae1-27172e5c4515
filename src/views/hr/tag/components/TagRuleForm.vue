<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
       <el-form-item label="规则名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入规则名称" />
      </el-form-item>
      <el-form-item label="规则类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择规则类型">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.TAG_RULE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="规则内容" prop="content">
        <Editor v-model="formData.content" height="150px" />
      </el-form-item>
      <el-form-item label="规则参数" prop="params">
        <el-input v-model="formData.params" placeholder="请输入规则参数" />
      </el-form-item>
      <el-form-item label="启用状态" prop="enabled">
        <el-radio-group v-model="formData.enabled">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="显示顺序" prop="displayOrder">
        <el-input v-model="formData.displayOrder" placeholder="请输入显示顺序" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { TagCatalogApi, TagRule } from '@/api/hr/tag'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  tagId: undefined,
  name: undefined,
  type: undefined,
  content: undefined,
  params: undefined,
  enabled: undefined,
  displayOrder: undefined
})
const formRules = reactive({
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number, tagId?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  formData.value.tagId = tagId  as any
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TagCatalogApi.getTagRule(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TagRule
    if (formType.value === 'create') {
      await TagCatalogApi.createTagRule(data)
      message.success(t('common.createSuccess'))
    } else {
      await TagCatalogApi.updateTagRule(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    tagId: undefined,
    name: undefined,
    type: undefined,
    content: undefined,
    params: undefined,
    enabled: undefined,
    displayOrder: undefined
  }
  formRef.value?.resetFields()
}
</script>