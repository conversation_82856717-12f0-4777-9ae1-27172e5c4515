# PersonController 标签计算API文档

## 概述
在PersonController中新增了4个标签计算相关的API接口，用于支持前端调用人员标签的自动计算功能。

## API接口列表

### 1. 计算指定人员的自动标签

**接口地址**: `POST /hr/person/calculate-tags`

**接口描述**: 为指定的人员计算所有自动标签

**请求参数**:
- `personId` (Long, 必填): 人员ID

**权限要求**: `hr:person:update`

**响应示例**:
```json
{
  "code": 0,
  "data": true,
  "msg": ""
}
```

**使用场景**: 
- 人员信息更新后，重新计算该人员的标签
- 手动触发单个人员的标签计算

---

### 2. 计算所有人员的自动标签（异步执行）

**接口地址**: `POST /hr/person/calculate-all-tags`

**接口描述**: 异步计算所有人员的自动标签，适用于批量处理

**请求参数**: 无

**权限要求**: `hr:person:update`

**响应示例**:
```json
{
  "code": 0,
  "data": true,
  "msg": ""
}
```

**特点**:
- **异步执行**: 使用专用线程池`tagCalculationExecutor`异步处理
- **非阻塞**: 接口立即返回，不等待计算完成
- **日志记录**: 详细记录执行过程和耗时
- **错误处理**: 异常不会影响接口响应

**使用场景**:
- 系统初始化时批量计算所有人员标签
- 定期重新计算所有人员标签
- 标签规则更新后的全量重算

---

### 3. 重新计算所有人员的指定标签

**接口地址**: `POST /hr/person/recalculate-tag`

**接口描述**: 根据标签ID重新计算所有人员的该标签

**请求参数**:
- `tagId` (Long, 必填): 标签ID

**权限要求**: `hr:person:update`

**响应示例**:
```json
{
  "code": 0,
  "data": true,
  "msg": ""
}
```

**使用场景**:
- 标签规则修改后，重新计算该标签
- 标签数据异常时的修复操作

---

### 4. 清理过期标签

**接口地址**: `POST /hr/person/clean-expired-tags`

**接口描述**: 清理所有过期的标签数据

**请求参数**: 无

**权限要求**: `hr:person:update`

**响应示例**:
```json
{
  "code": 0,
  "data": 5,
  "msg": ""
}
```

**响应说明**: `data`字段返回清理的标签数量

**使用场景**:
- 定期清理过期标签，释放存储空间
- 数据维护和清理操作

---

## 技术实现细节

### 异步处理配置

在`AsyncConfig`中新增了专用的标签计算线程池：

```java
@Bean("tagCalculationExecutor")
public Executor tagCalculationExecutor() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setCorePoolSize(3);        // 核心线程数
    executor.setMaxPoolSize(8);         // 最大线程数
    executor.setQueueCapacity(200);     // 队列容量
    executor.setThreadNamePrefix("tag-calc-");
    // ... 其他配置
    return executor;
}
```

### 日志记录

异步计算方法包含详细的日志记录：
- 开始时间和结束时间
- 处理的人员数量
- 执行耗时统计
- 异常情况记录

### 错误处理

- 同步接口：异常会直接返回错误响应
- 异步接口：异常被捕获并记录日志，不影响接口响应
- 所有接口都有完整的权限校验

## 前端调用示例

### JavaScript/TypeScript 调用示例

```javascript
// 1. 计算指定人员标签
async function calculatePersonTags(personId) {
  try {
    const response = await fetch('/hr/person/calculate-tags', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': 'Bearer ' + token
      },
      body: `personId=${personId}`
    });
    const result = await response.json();
    if (result.code === 0) {
      console.log('标签计算成功');
    }
  } catch (error) {
    console.error('标签计算失败:', error);
  }
}

// 2. 异步计算所有人员标签
async function calculateAllPersonTags() {
  try {
    const response = await fetch('/hr/person/calculate-all-tags', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + token
      }
    });
    const result = await response.json();
    if (result.code === 0) {
      console.log('批量标签计算已启动，请稍后查看结果');
    }
  } catch (error) {
    console.error('批量标签计算启动失败:', error);
  }
}

// 3. 重新计算指定标签
async function recalculateTag(tagId) {
  try {
    const response = await fetch('/hr/person/recalculate-tag', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': 'Bearer ' + token
      },
      body: `tagId=${tagId}`
    });
    const result = await response.json();
    if (result.code === 0) {
      console.log('标签重新计算成功');
    }
  } catch (error) {
    console.error('标签重新计算失败:', error);
  }
}

// 4. 清理过期标签
async function cleanExpiredTags() {
  try {
    const response = await fetch('/hr/person/clean-expired-tags', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + token
      }
    });
    const result = await response.json();
    if (result.code === 0) {
      console.log(`成功清理 ${result.data} 个过期标签`);
    }
  } catch (error) {
    console.error('清理过期标签失败:', error);
  }
}
```

## 注意事项

1. **权限控制**: 所有接口都需要`hr:person:update`权限
2. **异步处理**: `calculate-all-tags`接口是异步的，需要通过日志或其他方式监控执行状态
3. **性能考虑**: 批量计算可能耗时较长，建议在业务低峰期执行
4. **错误处理**: 前端需要妥善处理各种异常情况
5. **日志监控**: 可以通过应用日志监控标签计算的执行情况

## 相关依赖

这些API依赖于以下服务和组件：
- `PersonTagService`: 标签服务
- `PersonService`: 人员服务  
- `AsyncConfig`: 异步配置
- 标签计算框架（第一阶段已实现）
