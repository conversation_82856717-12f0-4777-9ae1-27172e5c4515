# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Essential Commands
- `pnpm i` - Install dependencies
- `pnpm dev` - Start development server (local environment)
- `pnpm build:local` - Build for local environment
- `pnpm build:prod` - Build for production

### Environment-specific Development
- `pnpm dev-dev` - Development server with production mode
- `pnpm dev-server` - Development server with dev mode
- `pnpm build:dev` - Build for development
- `pnpm build:test` - Build for test environment
- `pnpm build:stage` - Build for staging environment

### Code Quality & Linting
- `pnpm ts:check` - TypeScript type checking
- `pnpm lint:eslint` - ESLint code linting
- `pnpm lint:format` - Prettier code formatting
- `pnpm lint:style` - Stylelint CSS linting
- `pnpm lint:lint-staged` - Run lint-staged for pre-commit hooks

### Preview & Testing
- `pnpm preview` - Build and preview locally
- `pnpm serve:dev` - Preview development build
- `pnpm serve:prod` - Preview production build

## Architecture Overview

### Core Technology Stack
- **Frontend Framework**: Vue 3 + TypeScript + Vite
- **UI Component Library**: Element Plus
- **State Management**: Pinia with persistence
- **Routing**: Vue Router 4 with dynamic routes
- **HTTP Client**: Axios with custom request/response interceptors
- **Styling**: SCSS + UnoCSS atomic CSS
- **Build Tool**: Vite with custom plugin configuration

### Project Structure
```
src/
├── api/           # API service layers organized by business modules
├── components/    # Global reusable components
├── hooks/         # Vue 3 composition API hooks
├── layout/        # Layout components (header, sidebar, etc.)
├── router/        # Vue Router configuration
├── store/         # Pinia store modules
├── styles/        # Global styles and theme configurations
├── utils/         # Utility functions and helpers
└── views/         # Page components organized by business modules
```

### Key Architecture Patterns

**Modular API Structure**: APIs are organized by business domains (system, crm, bpm, etc.) with consistent response handling through axios interceptors.

**Permission System**: Dynamic routing with role-based access control. Routes are generated server-side and added dynamically via `permission.ts`.

**Component Architecture**: Heavy use of composition API with custom hooks for reusable logic. Components follow Element Plus design patterns.

**State Management**: Pinia stores are organized by feature (user, app, permission, etc.) with persistence for auth tokens and user preferences.

**Multi-environment Support**: Comprehensive environment configuration supporting local, dev, test, stage, and production deployments.

### Development Workflow

1. **Code Quality**: Always run `pnpm lint:eslint` and `pnpm ts:check` before commits
2. **Styling**: Use UnoCSS atomic classes where possible, SCSS for complex styling
3. **Component Development**: Follow Element Plus patterns and use composition API
4. **API Integration**: Use the centralized API service pattern under `src/api/`
5. **State Management**: Use Pinia stores for shared state, local state for component-specific data

### Build Configuration

The project uses Vite with custom optimization:
- **Code Splitting**: ECharts and FormCreate are bundled separately
- **Legacy Support**: Includes legacy browser support via @vitejs/plugin-legacy
- **Asset Optimization**: Automatic compression and minification
- **Environment Variables**: Extensive environment-specific configuration

### Key Dependencies
- **UI/UX**: Element Plus, UnoCSS, animate.css
- **Forms**: FormCreate with Element UI integration
- **Charts**: ECharts for data visualization
- **Business Logic**: Specialized modules for BPM, CRM, ERP, IoT
- **Development**: Hot reload, TypeScript support, comprehensive linting

When working with this codebase, prioritize consistency with existing patterns, especially the API service structure and component organization principles.